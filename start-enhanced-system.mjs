#!/usr/bin/env node

import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ANSI color codes for better output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  const timestamp = new Date().toISOString();
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
}

function logSection(title) {
  console.log('\n' + '='.repeat(80));
  log(title, 'cyan');
  console.log('='.repeat(80));
}

async function checkEnvironment() {
  logSection('🔍 ENVIRONMENT VALIDATION');
  
  const requiredFiles = [
    'package.json',
    'backend/config/index.ts',
    'backend/server-enhanced.ts',
    'backend/services/DatabaseConnectionManager.ts',
    'backend/services/EnhancedCacheService.ts',
    'backend/services/DataRoutingService.ts',
    'backend/services/PerformanceMonitoringService.ts',
    'backend/websocket/EnhancedWebSocketService.ts',
    'index.html'
  ];

  for (const file of requiredFiles) {
    try {
      await fs.access(path.join(__dirname, file));
      log(`✅ ${file} exists`, 'green');
    } catch (error) {
      log(`❌ ${file} missing`, 'red');
      throw new Error(`Required file missing: ${file}`);
    }
  }

  // Check environment variables
  const requiredEnvVars = [
    'REDIS_URL',
    'INFLUXDB_URL',
    'INFLUXDB_TOKEN',
    'INFLUXDB_ORG',
    'INFLUXDB_BUCKET'
  ];

  const missingEnvVars = [];
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      missingEnvVars.push(envVar);
    }
  }

  if (missingEnvVars.length > 0) {
    log(`⚠️  Missing environment variables: ${missingEnvVars.join(', ')}`, 'yellow');
    log('Some features may not work properly', 'yellow');
  } else {
    log('✅ All required environment variables present', 'green');
  }

  log('Environment validation completed', 'green');
}

async function installDependencies() {
  logSection('📦 DEPENDENCY INSTALLATION');
  
  return new Promise((resolve, reject) => {
    log('Installing/updating dependencies...', 'blue');
    
    const npm = spawn('npm', ['install'], {
      stdio: 'pipe',
      cwd: __dirname
    });

    let output = '';
    npm.stdout.on('data', (data) => {
      output += data.toString();
    });

    npm.stderr.on('data', (data) => {
      output += data.toString();
    });

    npm.on('close', (code) => {
      if (code === 0) {
        log('✅ Dependencies installed successfully', 'green');
        resolve();
      } else {
        log('❌ Dependency installation failed', 'red');
        console.log(output);
        reject(new Error('npm install failed'));
      }
    });
  });
}

async function buildProject() {
  logSection('🔨 PROJECT BUILD');
  
  return new Promise((resolve, reject) => {
    log('Building TypeScript project...', 'blue');
    
    const tsc = spawn('npx', ['tsc', '-p', 'backend/tsconfig.json'], {
      stdio: 'pipe',
      cwd: __dirname
    });

    let output = '';
    tsc.stdout.on('data', (data) => {
      output += data.toString();
    });

    tsc.stderr.on('data', (data) => {
      output += data.toString();
    });

    tsc.on('close', (code) => {
      if (code === 0) {
        log('✅ TypeScript compilation successful', 'green');
        resolve();
      } else {
        log('❌ TypeScript compilation failed', 'red');
        console.log(output);
        reject(new Error('TypeScript compilation failed'));
      }
    });
  });
}

async function testDatabaseConnections() {
  logSection('🗄️  DATABASE CONNECTION TESTING');
  
  const tests = [
    {
      name: 'Redis',
      test: async () => {
        const { createClient } = await import('redis');
        const client = createClient({
          url: process.env.REDIS_URL || 'redis://localhost:6379'
        });
        await client.connect();
        await client.ping();
        await client.quit();
        return true;
      }
    },
    {
      name: 'InfluxDB',
      test: async () => {
        const { InfluxDB } = await import('@influxdata/influxdb-client');
        const influxDB = new InfluxDB({
          url: process.env.INFLUXDB_URL || 'http://localhost:8086',
          token: process.env.INFLUXDB_TOKEN || 'test-token'
        });
        const queryApi = influxDB.getQueryApi(process.env.INFLUXDB_ORG || 'test-org');
        await queryApi.queryRaw('buckets()');
        return true;
      }
    }
  ];

  for (const { name, test } of tests) {
    try {
      await test();
      log(`✅ ${name} connection successful`, 'green');
    } catch (error) {
      log(`⚠️  ${name} connection failed: ${error.message}`, 'yellow');
      log(`${name} features may not work properly`, 'yellow');
    }
  }
}

async function startEnhancedServer() {
  logSection('🚀 ENHANCED SERVER STARTUP');
  
  return new Promise((resolve, reject) => {
    log('Starting Enhanced MEV Arbitrage Bot Server...', 'blue');
    
    const server = spawn('npx', ['tsx', 'backend/server-enhanced.ts'], {
      stdio: 'pipe',
      cwd: __dirname,
      env: {
        ...process.env,
        NODE_ENV: process.env.NODE_ENV || 'development'
      }
    });

    let serverReady = false;
    let startupTimeout;

    server.stdout.on('data', (data) => {
      const output = data.toString();
      console.log(output);
      
      if (output.includes('Enhanced MEV Arbitrage Bot Server running') && !serverReady) {
        serverReady = true;
        clearTimeout(startupTimeout);
        log('✅ Enhanced server started successfully', 'green');
        resolve(server);
      }
    });

    server.stderr.on('data', (data) => {
      const output = data.toString();
      console.error(output);
      
      if (output.includes('Error') || output.includes('Failed')) {
        log('❌ Server startup error detected', 'red');
      }
    });

    server.on('close', (code) => {
      if (!serverReady) {
        log(`❌ Server exited with code ${code}`, 'red');
        reject(new Error(`Server startup failed with code ${code}`));
      }
    });

    // Set startup timeout
    startupTimeout = setTimeout(() => {
      if (!serverReady) {
        log('❌ Server startup timeout (60 seconds)', 'red');
        server.kill();
        reject(new Error('Server startup timeout'));
      }
    }, 60000);
  });
}

async function performHealthCheck() {
  logSection('🏥 SYSTEM HEALTH CHECK');
  
  const maxRetries = 10;
  const retryDelay = 2000;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      const response = await fetch('http://localhost:8080/health');
      const health = await response.json();
      
      if (health.success) {
        log('✅ Health check passed', 'green');
        log(`System status: ${health.data.status}`, 'green');
        log(`System score: ${health.data.score}/100`, 'green');
        
        // Log service status
        if (health.data.services) {
          Object.entries(health.data.services).forEach(([service, status]) => {
            const statusColor = status ? 'green' : 'red';
            const statusIcon = status ? '✅' : '❌';
            log(`${statusIcon} ${service}: ${status ? 'healthy' : 'unhealthy'}`, statusColor);
          });
        }
        
        return true;
      } else {
        throw new Error('Health check failed');
      }
    } catch (error) {
      if (i === maxRetries - 1) {
        log('❌ Health check failed after maximum retries', 'red');
        throw error;
      }
      
      log(`⏳ Health check attempt ${i + 1}/${maxRetries} failed, retrying in ${retryDelay/1000}s...`, 'yellow');
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }
}

async function displaySystemInfo() {
  logSection('📊 SYSTEM INFORMATION');
  
  try {
    const [healthResponse, metricsResponse, integrationResponse] = await Promise.all([
      fetch('http://localhost:8080/health'),
      fetch('http://localhost:8080/api/system/metrics'),
      fetch('http://localhost:8080/api/system/integration')
    ]);

    const health = await healthResponse.json();
    const metrics = await metricsResponse.json();
    const integration = await integrationResponse.json();

    log('🌐 Server Information:', 'cyan');
    log(`   • HTTP Server: http://localhost:8080`, 'blue');
    log(`   • WebSocket: ws://localhost:8080/ws`, 'blue');
    log(`   • Frontend: file://${path.join(__dirname, 'index.html')}`, 'blue');

    log('\n📈 Performance Metrics:', 'cyan');
    if (metrics.success && metrics.data.performance) {
      const perf = metrics.data.performance;
      log(`   • Average Latency: ${perf.averageLatency?.toFixed(2) || 0}ms`, 'blue');
      log(`   • System Uptime: ${perf.systemUptime?.toFixed(2) || 0}%`, 'blue');
      log(`   • Cache Hit Ratio: ${perf.cacheHitRatio?.toFixed(2) || 0}%`, 'blue');
    }

    log('\n🔧 Enabled Features:', 'cyan');
    if (integration.success && integration.data.enabledFeatures) {
      Object.entries(integration.data.enabledFeatures).forEach(([feature, enabled]) => {
        const icon = enabled ? '✅' : '❌';
        const color = enabled ? 'green' : 'red';
        log(`   ${icon} ${feature}`, color);
      });
    }

    log('\n🎯 Quick Start:', 'cyan');
    log('   1. Open the frontend in your browser:', 'blue');
    log(`      file://${path.join(__dirname, 'index.html')}`, 'blue');
    log('   2. Monitor real-time data via WebSocket connection', 'blue');
    log('   3. Check system health: http://localhost:8080/health', 'blue');
    log('   4. View metrics: http://localhost:8080/api/system/metrics', 'blue');

  } catch (error) {
    log('⚠️  Could not fetch system information', 'yellow');
  }
}

async function main() {
  try {
    console.log(`${colors.bright}${colors.magenta}`);
    console.log('╔══════════════════════════════════════════════════════════════════════════════╗');
    console.log('║                    ENHANCED MEV ARBITRAGE BOT SYSTEM                        ║');
    console.log('║                         Comprehensive Startup                               ║');
    console.log('╚══════════════════════════════════════════════════════════════════════════════╝');
    console.log(colors.reset);

    // Step 1: Environment validation
    await checkEnvironment();

    // Step 2: Install dependencies
    await installDependencies();

    // Step 3: Build project
    await buildProject();

    // Step 4: Test database connections
    await testDatabaseConnections();

    // Step 5: Start enhanced server
    const serverProcess = await startEnhancedServer();

    // Step 6: Perform health check
    await performHealthCheck();

    // Step 7: Display system information
    await displaySystemInfo();

    logSection('🎉 STARTUP COMPLETE');
    log('Enhanced MEV Arbitrage Bot System is fully operational!', 'green');
    log('Press Ctrl+C to shutdown gracefully', 'yellow');

    // Handle graceful shutdown
    process.on('SIGINT', () => {
      log('\n🛑 Shutdown signal received, stopping server...', 'yellow');
      serverProcess.kill('SIGTERM');
      process.exit(0);
    });

    // Keep the process alive
    process.stdin.resume();

  } catch (error) {
    log(`❌ Startup failed: ${error.message}`, 'red');
    console.error(error);
    process.exit(1);
  }
}

// Run the startup sequence
main();
